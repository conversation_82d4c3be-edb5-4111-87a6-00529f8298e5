import requests
import json
import time

# 获取新的access_token
access_token = '94_4M7zkf3Uo2D0RrUwFxTclwqoO3buwNzsIMmqH5p1c_Ha3mLVKfDRlM6bC24KrsR9nbo_9FTc_mypeUxPbOADW0Y-SMk0-ZHW6_cJRGCE0Cd4SbkMrX71n9_eT7UCPAbAFAFSD'

# 下载项老师logo作为封面图片
logo_url = 'https://www.diwangzhidao.com/logo.png'
print('下载项老师logo...')

try:
    response = requests.get(logo_url, timeout=30)
    if response.status_code == 200:
        with open('logo.png', 'wb') as f:
            f.write(response.content)
        print('✅ logo下载成功')
        
        # 上传图片到微信服务器
        upload_url = f'https://api.weixin.qq.com/cgi-bin/media/upload?access_token={access_token}&type=thumb'
        
        with open('logo.png', 'rb') as f:
            files = {'media': ('logo.png', f, 'image/png')}
            upload_response = requests.post(upload_url, files=files, timeout=30)
            
        print(f'上传响应状态码: {upload_response.status_code}')
        print(f'上传响应内容: {upload_response.text}')
        
        if upload_response.status_code == 200:
            result = upload_response.json()
            if 'media_id' in result:
                media_id = result['media_id']
                print(f'✅ 图片上传成功，media_id: {media_id}')
                
                # 保存media_id
                with open('media_ids.json', 'w', encoding='utf-8') as f:
                    json.dump({
                        'thumb_media_id': media_id,
                        'upload_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'success': True
                    }, f, ensure_ascii=False, indent=2)
                
            else:
                print('❌ 上传失败，返回数据异常')
                print(f'返回结果: {result}')
        else:
            print(f'❌ 上传失败，状态码: {upload_response.status_code}')
            
    else:
        print(f'❌ logo下载失败，状态码: {response.status_code}')
        
except Exception as e:
    print(f'❌ 操作失败: {e}')

print('图片上传流程结束')
