import requests
import json
import time

# 最新的access_token
access_token = '94_a-j9rJ0Hh9UMjtwoewYr2w7--q40hgdrj7YVY3cOg5Nh8f32hxNL8OjjXGJee4nq5vh06s7NPNCJ7mGnRJmc3lucZWzGDoXDMMoPGkRrsfNEsufFSzZSgk70w-UIXGhABAQFO'

print('使用正确的永久素材media_id上传草稿...')

# 读取更新后的JSON数据
with open('draft_data.json', 'r', encoding='utf-8') as f:
    draft_data = json.load(f)

print(f'包含文章数量: {len(draft_data["articles"])}')
print(f'使用的media_id: {draft_data["articles"][0]["thumb_media_id"][:20]}...')

# 草稿创建API
draft_url = f'https://api.weixin.qq.com/cgi-bin/draft/add?access_token={access_token}'

# 按照协议要求的中文编码方式
json_content = json.dumps(draft_data, ensure_ascii=False)
data = json_content.encode('utf-8')

headers = {
    'Content-Type': 'application/json; charset=utf-8'
}

try:
    print('正在上传草稿到微信公众号...')
    response = requests.post(draft_url, data=data, headers=headers, timeout=30)
    
    print(f'草稿上传响应状态码: {response.status_code}')
    print(f'草稿上传响应内容: {response.text}')
    
    if response.status_code == 200:
        result = response.json()
        if 'media_id' in result and result['media_id']:
            draft_media_id = result['media_id']
            print(f'🎉 草稿上传成功！')
            print(f'草稿media_id: {draft_media_id}')
            print(f'请登录微信公众号后台查看草稿箱')
            
            # 保存成功结果
            with open('final_upload_success.json', 'w', encoding='utf-8') as f:
                json.dump({
                    'success': True,
                    'draft_media_id': draft_media_id,
                    'upload_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'articles_count': len(draft_data['articles']),
                    'permanent_media_id': draft_data['articles'][0]['thumb_media_id'],
                    'response': result,
                    'articles_titles': [
                        draft_data['articles'][0]['title'],
                        draft_data['articles'][1]['title'],
                        draft_data['articles'][2]['title'],
                        draft_data['articles'][3]['title']
                    ]
                }, f, ensure_ascii=False, indent=2)
            
            print('✅ 草稿上传结果已保存')
            
        else:
            print('❌ 草稿上传失败，返回数据异常')
            print(f'返回结果: {result}')
    else:
        print(f'❌ 草稿上传失败，状态码: {response.status_code}')
        print(f'错误信息: {response.text}')
        
except Exception as e:
    print(f'❌ 请求失败: {e}')

print('最终草稿上传流程结束')
