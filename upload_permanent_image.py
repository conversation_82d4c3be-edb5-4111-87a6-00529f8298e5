import requests
import json
import time

# 最新的access_token
access_token = '94_a-j9rJ0Hh9UMjtwoewYr2w7--q40hgdrj7YVY3cOg5Nh8f32hxNL8OjjXGJee4nq5vh06s7NPNCJ7mGnRJmc3lucZWzGDoXDMMoPGkRrsfNEsufFSzZSgk70w-UIXGhABAQFO'

print('按照协议要求上传永久素材图片...')

# 协议指定的图片上传API
upload_url = f'https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={access_token}&type=image'

try:
    # 下载项老师logo
    logo_url = 'https://www.diwangzhidao.com/logo.png'
    response = requests.get(logo_url, timeout=30)
    
    if response.status_code == 200:
        with open('permanent_logo.png', 'wb') as f:
            f.write(response.content)
        print('✅ 项老师logo下载成功')
        
        # 上传为永久素材
        with open('permanent_logo.png', 'rb') as f:
            files = {'media': ('permanent_logo.png', f, 'image/png')}
            upload_response = requests.post(upload_url, files=files, timeout=30)
            
        print(f'永久素材上传响应状态码: {upload_response.status_code}')
        print(f'永久素材上传响应内容: {upload_response.text}')
        
        if upload_response.status_code == 200:
            result = upload_response.json()
            if 'media_id' in result:
                permanent_media_id = result['media_id']
                print(f'✅ 永久素材上传成功！')
                print(f'永久素材media_id: {permanent_media_id}')
                
                # 保存永久素材media_id
                with open('permanent_media_result.json', 'w', encoding='utf-8') as f:
                    json.dump({
                        'success': True,
                        'permanent_media_id': permanent_media_id,
                        'upload_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                        'url': result.get('url', ''),
                        'response': result
                    }, f, ensure_ascii=False, indent=2)
                
                print('✅ 永久素材media_id已保存，可用于草稿创建')
                
            else:
                print('❌ 永久素材上传失败，返回数据异常')
                print(f'返回结果: {result}')
        else:
            print(f'❌ 永久素材上传失败，状态码: {upload_response.status_code}')
            print(f'错误信息: {upload_response.text}')
    else:
        print(f'❌ logo下载失败，状态码: {response.status_code}')
        
except Exception as e:
    print(f'❌ 操作失败: {e}')

print('永久素材上传流程结束')
