import requests
import json
import time

# 最新的access_token
access_token = '94_a-j9rJ0Hh9UMjtwoewYr2w7--q40hgdrj7YVY3cOg5Nh8f32hxNL8OjjXGJee4nq5vh06s7NPNCJ7mGnRJmc3lucZWzGDoXDMMoPGkRrsfNEsufFSzZSgk70w-UIXGhABAQFO'

print('开始创建测试草稿...')

# 先上传一张图片获取media_id
print('1. 上传图片获取media_id...')
logo_url = 'https://www.diwangzhidao.com/logo.png'

try:
    # 下载图片
    response = requests.get(logo_url, timeout=30)
    if response.status_code == 200:
        with open('test_logo.png', 'wb') as f:
            f.write(response.content)
        print('✅ 图片下载成功')
        
        # 上传为临时素材
        upload_url = f'https://api.weixin.qq.com/cgi-bin/media/upload?access_token={access_token}&type=thumb'
        
        with open('test_logo.png', 'rb') as f:
            files = {'media': ('test_logo.png', f, 'image/png')}
            upload_response = requests.post(upload_url, files=files, timeout=30)
            
        print(f'上传响应: {upload_response.text}')
        
        if upload_response.status_code == 200:
            upload_result = upload_response.json()
            if 'thumb_media_id' in upload_result:
                thumb_media_id = upload_result['thumb_media_id']
                print(f'✅ 图片上传成功，thumb_media_id: {thumb_media_id}')
                
                # 创建简单的单篇文章草稿
                print('2. 创建单篇文章草稿...')
                
                draft_data = {
                    "articles": [
                        {
                            "title": "测试文章 - 项老师AI工作室",
                            "author": "项老师超级总裁助理001",
                            "digest": "这是一篇测试文章，验证微信公众号草稿功能是否正常工作。",
                            "content": "<p>尊敬的项老师，您好！</p><p>这是一篇测试文章，用于验证微信公众号草稿API是否正常工作。</p><p>如果您能在微信公众号后台看到这篇草稿，说明系统运行正常。</p><p>接下来我将创建完整的4篇文章草稿。</p><p>此致<br>敬礼！</p><p><strong>项老师超级总裁助理001</strong></p>",
                            "content_source_url": "https://www.diwangzhidao.com/",
                            "thumb_media_id": thumb_media_id,
                            "show_cover_pic": 1,
                            "need_open_comment": 0,
                            "only_fans_can_comment": 0
                        }
                    ]
                }
                
                # 上传草稿
                draft_url = f'https://api.weixin.qq.com/cgi-bin/draft/add?access_token={access_token}'
                
                headers = {
                    'Content-Type': 'application/json; charset=utf-8'
                }
                
                draft_response = requests.post(draft_url, json=draft_data, headers=headers, timeout=30)
                
                print(f'草稿创建响应状态码: {draft_response.status_code}')
                print(f'草稿创建响应内容: {draft_response.text}')
                
                if draft_response.status_code == 200:
                    draft_result = draft_response.json()
                    if 'media_id' in draft_result:
                        draft_media_id = draft_result['media_id']
                        print(f'✅ 测试草稿创建成功！')
                        print(f'草稿media_id: {draft_media_id}')
                        print(f'请登录微信公众号后台查看草稿箱')
                        
                        # 保存结果
                        with open('test_draft_result.json', 'w', encoding='utf-8') as f:
                            json.dump({
                                'success': True,
                                'draft_media_id': draft_media_id,
                                'thumb_media_id': thumb_media_id,
                                'create_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                                'response': draft_result
                            }, f, ensure_ascii=False, indent=2)
                        
                    else:
                        print('❌ 草稿创建失败')
                        print(f'错误信息: {draft_result}')
                else:
                    print(f'❌ 草稿创建失败，状态码: {draft_response.status_code}')
                    print(f'错误信息: {draft_response.text}')
                    
            else:
                print('❌ 图片上传失败，未获取到thumb_media_id')
        else:
            print(f'❌ 图片上传失败，状态码: {upload_response.status_code}')
    else:
        print(f'❌ 图片下载失败，状态码: {response.status_code}')
        
except Exception as e:
    print(f'❌ 操作失败: {e}')

print('测试草稿创建流程结束')
