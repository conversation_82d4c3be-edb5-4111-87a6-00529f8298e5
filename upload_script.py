import json
import requests
import time

# 读取JSON数据
with open('draft_data.json', 'r', encoding='utf-8') as f:
    draft_data = json.load(f)

# 真实的access_token
access_token = '94_4M7zkf3Uo2D0RrUwFxTclwqoO3buwNzsIMmqH5p1c_Ha3mLVKfDRlM6bC24KrsR9nbo_9FTc_mypeUxPbOADW0Y-SMk0-ZHW6_cJRGCE0Cd4SbkMrX71n9_eT7UCPAbAFAFSD'
api_url = f'https://api.weixin.qq.com/cgi-bin/draft/add?access_token={access_token}'

print('开始真实上传草稿到微信公众号...')
print(f'包含文章数量: {len(draft_data["articles"])}')

# 构建请求数据
headers = {
    'Content-Type': 'application/json; charset=utf-8'
}

try:
    # 发送POST请求
    response = requests.post(api_url, json=draft_data, headers=headers, timeout=30)
    
    print(f'响应状态码: {response.status_code}')
    print(f'响应内容: {response.text}')
    
    if response.status_code == 200:
        result = response.json()
        if 'media_id' in result and result['media_id']:
            draft_media_id = result['media_id']
            print(f'✅ 草稿上传成功，media_id: {draft_media_id}')
            
            # 保存真实结果
            with open('real_upload_result.json', 'w', encoding='utf-8') as f:
                json.dump({
                    'success': True,
                    'media_id': draft_media_id,
                    'upload_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'articles_count': len(draft_data['articles']),
                    'response': result
                }, f, ensure_ascii=False, indent=2)
            
        else:
            print('❌ 草稿上传失败，返回数据异常')
            print(f'返回结果: {result}')
    else:
        print(f'❌ 草稿上传失败，状态码: {response.status_code}')
        print(f'错误信息: {response.text}')

except Exception as e:
    print(f'❌ 请求失败: {e}')

print('草稿上传流程结束')
