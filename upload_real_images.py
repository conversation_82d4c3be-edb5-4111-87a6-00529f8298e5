import requests
import json
import time
import random
import os

# 最新的access_token
access_token = '94_a-j9rJ0Hh9UMjtwoewYr2w7--q40hgdrj7YVY3cOg5Nh8f32hxNL8OjjXGJee4nq5vh06s7NPNCJ7mGnRJmc3lucZWzGDoXDMMoPGkRrsfNEsufFSzZSgk70w-UIXGhABAQFO'

print('使用项老师真实图片库上传4张不同封面图片...')

# 真实的图片库路径
image_path = r'C:\Users\<USER>\Desktop\项老师AI协议\公众号素材\tu'
print(f'图片库路径: {image_path}')

# 检查图片库
if os.path.exists(image_path):
    jpg_files = [f for f in os.listdir(image_path) if f.endswith('.jpg') and f[:-4].isdigit()]
    print(f'✅ 找到 {len(jpg_files)} 张数字命名的图片')
    
    # 随机选择4张不重复的图片
    random.seed(int(time.time()))
    selected_images = random.sample(jpg_files, 4)
    print(f'随机选择的4张图片: {selected_images}')
    
    media_ids = []
    
    for i, image_name in enumerate(selected_images, 1):
        image_file_path = os.path.join(image_path, image_name)
        print(f'\\n上传第{i}张图片: {image_name}')
        
        try:
            # 上传为永久素材
            upload_url = f'https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={access_token}&type=image'
            
            with open(image_file_path, 'rb') as f:
                files = {'media': (image_name, f, 'image/jpeg')}
                upload_response = requests.post(upload_url, files=files, timeout=30)
                
            print(f'上传响应状态码: {upload_response.status_code}')
            print(f'上传响应内容: {upload_response.text}')
            
            if upload_response.status_code == 200:
                result = upload_response.json()
                if 'media_id' in result:
                    media_id = result['media_id']
                    media_ids.append(media_id)
                    print(f'✅ 第{i}张图片上传成功，media_id: {media_id}')
                else:
                    print(f'❌ 第{i}张图片上传失败，返回数据异常')
                    media_ids.append(None)
            else:
                print(f'❌ 第{i}张图片上传失败，状态码: {upload_response.status_code}')
                media_ids.append(None)
                
        except Exception as e:
            print(f'❌ 第{i}张图片上传异常: {e}')
            media_ids.append(None)
    
    # 保存结果
    result_data = {
        'success': True,
        'upload_time': time.strftime('%Y-%m-%d %H:%M:%S'),
        'selected_images': selected_images,
        'media_ids': media_ids,
        'image_path': image_path
    }
    
    with open('real_images_upload_result.json', 'w', encoding='utf-8') as f:
        json.dump(result_data, f, ensure_ascii=False, indent=2)
    
    print(f'\\n✅ 4张真实图片上传完成')
    print(f'成功上传: {len([mid for mid in media_ids if mid is not None])} 张')
    
else:
    print(f'❌ 图片库路径不存在: {image_path}')

print('真实图片上传流程结束')
