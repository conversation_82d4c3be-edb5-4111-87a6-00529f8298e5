import requests
import json
import time

# 获取access_token
access_token = '94_4M7zkf3Uo2D0RrUwFxTclwqoO3buwNzsIMmqH5p1c_Ha3mLVKfDRlM6bC24KrsR9nbo_9FTc_mypeUxPbOADW0Y-SMk0-ZHW6_cJRGCE0Cd4SbkMrX71n9_eT7UCPAbAFAFSD'

print('开始上传永久素材...')

try:
    # 上传永久图片素材
    upload_url = f'https://api.weixin.qq.com/cgi-bin/material/add_material?access_token={access_token}&type=image'
    
    with open('logo.png', 'rb') as f:
        files = {'media': ('logo.png', f, 'image/png')}
        upload_response = requests.post(upload_url, files=files, timeout=30)
        
    print(f'上传响应状态码: {upload_response.status_code}')
    print(f'上传响应内容: {upload_response.text}')
    
    if upload_response.status_code == 200:
        result = upload_response.json()
        if 'media_id' in result:
            media_id = result['media_id']
            print(f'✅ 永久素材上传成功，media_id: {media_id}')
            
            # 保存永久素材media_id
            with open('permanent_media_ids.json', 'w', encoding='utf-8') as f:
                json.dump({
                    'permanent_media_id': media_id,
                    'upload_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'success': True,
                    'url': result.get('url', ''),
                    'response': result
                }, f, ensure_ascii=False, indent=2)
            
        else:
            print('❌ 上传失败，返回数据异常')
            print(f'返回结果: {result}')
    else:
        print(f'❌ 上传失败，状态码: {upload_response.status_code}')
        
except Exception as e:
    print(f'❌ 操作失败: {e}')

print('永久素材上传流程结束')
